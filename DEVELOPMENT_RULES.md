# X1-Base Development Rules

This document defines the development standards, conventions, and best practices for the x1-base project based on existing codebase patterns.

## 1. Code Organization Rules

### 1.1 Directory Structure Standards

**Top-level directories:**
- `component/` - Business logic components (e.g., blb, bcm, gmaster)
- `sdk/` - External service SDK wrappers (e.g., bcc, elb, iam)
- `model/` - Data models and database operations
- `utils/` - Utility functions and common helpers
- `task/` - Task execution framework components
- `logger/` - Logging infrastructure
- `common/` - Common shared code (errors, environment)
- `privatecloud/` - Private cloud specific implementations
- `middleware/` - HTTP middleware components
- `tools/` - Development and utility tools
- `conf_ut/` - Unit test configuration files

### 1.2 Package Naming Conventions

**Package names must:**
- Use lowercase letters only
- Use underscores for multi-word packages (e.g., `base_utils`, `debug_utils`)
- Be descriptive and concise
- Match the directory name exactly

**Examples:**
```go
package blb          // ✓ Good
package base_utils   // ✓ Good
package BLB          // ✗ Bad - uppercase
package blb_service  // ✗ Bad - unnecessary suffix
```

### 1.3 File Naming Conventions

**Go files must:**
- Use lowercase with underscores for separation
- Include descriptive names indicating purpose
- Follow patterns: `{component}.go`, `{component}_test.go`, `interface.go`, `conf.go`

**Examples:**
```
blb.go              // ✓ Main component file
blb_test.go         // ✓ Test file
interface.go        // ✓ Interface definitions
conf.go             // ✓ Configuration
logger_file.go      // ✓ Specific implementation
```

### 1.4 Import Organization

**Import order (separated by blank lines):**
1. Standard library imports
2. Third-party imports (github.com, etc.)
3. Baidu internal imports (icode.baidu.com/baidu/gdp/*)
4. Project internal imports (icode.baidu.com/baidu/scs/x1-base/*)

**Example:**
```go
import (
    "context"
    "sync"
    "time"

    "github.com/spf13/cast"
    "github.com/google/uuid"

    "icode.baidu.com/baidu/gdp/logit"
    "icode.baidu.com/baidu/gdp/extension/gtask"

    "icode.baidu.com/baidu/scs/x1-base/common/cerrs"
    "icode.baidu.com/baidu/scs/x1-base/logger"
    "icode.baidu.com/baidu/scs/x1-base/sdk/common"
)
```

## 2. Development Guidelines

### 2.1 Component Architecture Pattern

**All components must follow this structure:**
```go
type component struct {
    conf     *config
    sdk      SomeSDK
    initOnce sync.Once
}

var defaultComponent = &component{
    conf: &config{},
}

func Instance() ComponentInterface {
    defaultComponent.initOnce.Do(func() {
        // Initialize component
    })
    return defaultComponent
}
```

### 2.2 Error Handling Standards

**Use the custom error system from `common/cerrs`:**
```go
// Define component-specific errors
var (
    ErrComponentFail = cerrs.NewConst(cerrs.CODE_COMPONENT_BASE+1, "component operation failed", true, nil)
)

// Wrap errors with context
return cerrs.ErrInvalidParams.Errorf("invalid parameter: %s", param)

// Check error types
if cerrs.Is(err, cerrs.ErrNotFound) {
    // Handle not found
}
```

### 2.3 Logging Conventions

**Use structured logging with appropriate levels:**
```go
// Component operations
logger.ComponentLogger.Warning(ctx, "operation failed: %s", err.Error())

// SDK operations  
logger.SdkLogger.Trace(ctx, "API call: %s, params: %s", action, base_utils.Format(params))

// General application logs
logger.DefaultLogger.Notice(ctx, "process completed successfully")
```

### 2.4 Configuration Management

**Configuration files must:**
- Use TOML format
- Be placed in appropriate directories (`conf_ut/component/`, `conf_ut/servicer/`)
- Support IDC-specific overrides using `IDC_SPEC` sections
- Use `compo_utils.LoadConf()` for loading with IDC support

**Example configuration loading:**
```go
func MustLoadConf(ctx context.Context) {
    if err := compo_utils.LoadConf("component-name", &config); err != nil {
        panic(fmt.Sprintf("load config failed: %s", err.Error()))
    }
}
```

## 3. Dependency Management Rules

### 3.1 Go Module Management

**Always use Go modules with these practices:**
- Use `go mod download` for dependency installation
- Set proxy environment variables in Makefile:
  ```makefile
  $(GO) env -w GOPROXY=https://goproxy.baidu-int.com
  $(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
  $(GO) env -w GONOSUMDB=\*
  ```
- Pin specific versions for stability
- Use semantic versioning

### 3.2 Internal Dependencies

**Baidu internal packages:**
- GDP framework: `icode.baidu.com/baidu/gdp/*` (v1.20+)
- Internal SDKs: `icode.baidu.com/baidu/scs/thirdparty-private-sdks`
- IAM SDK: `icode.baidu.com/baidu/bce-iam/sdk-go`

### 3.3 External Dependencies

**Approved external packages:**
- Testing: `github.com/stretchr/testify`, `github.com/golang/mock`, `github.com/smartystreets/goconvey`
- Utilities: `github.com/spf13/cast`, `github.com/google/uuid`, `github.com/pkg/errors`
- Database: `gorm.io/gorm`, `gorm.io/driver/mysql`
- HTTP: `github.com/gin-gonic/gin`
- JSON: `github.com/json-iterator/go`, `github.com/goccy/go-json`

## 4. Testing Requirements

### 4.1 Test Organization

**Test files must:**
- End with `_test.go`
- Be in the same package as the code being tested
- Use table-driven tests for multiple scenarios
- Include both unit tests and integration tests

### 4.2 Test Setup Patterns

**Use standardized test initialization:**
```go
func init() {
    unittest.UnitTestInit(2) // Parameter indicates directory depth
}

func TestFunction(t *testing.T) {
    ctx := context.Background()
    defer sdk_utils.TestEnvDefer(ctx)()
    
    // Test implementation
}
```

### 4.3 Mock Usage

**Use gomock for interface mocking:**
```go
func TestWithMock(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mock := NewMockService(ctrl)
    mock.EXPECT().Method(gomock.Any(), gomock.Any()).Return(expectedResult, nil)
    
    // Test with mock
}
```

### 4.4 Coverage Requirements

**Test coverage standards:**
- Minimum 70% line coverage for new code
- All public interfaces must have tests
- Critical business logic requires 90%+ coverage
- Use `make test` to run tests with coverage

## 5. Documentation Standards

### 5.1 Code Comments

**File headers must include:**
```go
/*
 * Copyright(C) YYYY Baidu Inc. All Rights Reserved.
 * Author: Name (<EMAIL>)
 * Date: YYYY/MM/DD
 * File: filename.go
 */

/*
 * DESCRIPTION
 *   Brief description of file purpose
 */
```

### 5.2 Function Documentation

**Public functions must have godoc comments:**
```go
// ComponentOperation performs the specified operation on the component.
// It returns the operation result and any error encountered.
// 
// Parameters:
//   - ctx: Context for the operation
//   - params: Operation parameters
//
// Returns:
//   - result: Operation result
//   - error: Any error that occurred
func ComponentOperation(ctx context.Context, params *Params) (*Result, error) {
    // Implementation
}
```

### 5.3 Package Documentation

**Each package must have package-level documentation:**
```go
// Package blb provides BLB (Baidu Load Balancer) resource management functionality.
// It handles creation, modification, and deletion of load balancer resources
// through the BLB SDK integration.
package blb
```

### 5.4 Configuration Documentation

**Configuration files must include:**
- Inline comments explaining each parameter
- Example values where appropriate
- References to related documentation

**Example:**
```toml
# BLB component configuration
[BLB]
# API endpoint for BLB service
Endpoint = "https://blb.baidubce.com"
# Request timeout in milliseconds
Timeout = 30000
```

## 6. Build and Deployment Standards

### 6.1 Makefile Standards

**The project Makefile must include these targets:**
```makefile
all: prepare compile package    # Complete build process
prepare: gomod                  # Download dependencies
compile: build                  # Compile the binary
test: prepare test-case         # Run all tests
clean                          # Clean build artifacts
package: package-bin           # Package for deployment
```

### 6.2 Build Configuration

**Build settings:**
- Go version: 1.19 (specified in go.mod)
- Compiler: Use `GO_1_19_BIN` environment variable
- Output directory: `./output/`
- Binary name: `x1-base`

### 6.3 CI/CD Integration

**CI configuration (ci.yml):**
- Use DECK_CENTOS6U3_K3 image
- Resource type: SMALL
- Build command: `bash build.sh`
- Enable reuse checks
- Generate release artifacts

## 7. Security and Best Practices

### 7.1 Authentication and Authorization

**IAM Integration:**
- Use `icode.baidu.com/baidu/bce-iam/sdk-go` for authentication
- Store credentials securely in configuration files
- Implement proper token refresh mechanisms
- Log authentication failures appropriately

### 7.2 Input Validation

**Parameter validation:**
```go
// Validate required parameters
if params.UserID == "" {
    return cerrs.ErrInvalidParams.Errorf("UserID is required")
}

// Validate parameter formats
if !isValidFormat(params.ID) {
    return cerrs.ErrInvalidParams.Errorf("invalid ID format: %s", params.ID)
}
```

### 7.3 Resource Management

**Context and timeout handling:**
```go
// Always use context with timeout for external calls
ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
defer cancel()

// Check context cancellation
select {
case <-ctx.Done():
    return ctx.Err()
default:
    // Continue processing
}
```

### 7.4 Concurrent Programming

**Use sync.Once for initialization:**
```go
type component struct {
    initOnce sync.Once
    // other fields
}

func (c *component) init() {
    c.initOnce.Do(func() {
        // Initialization code here
    })
}
```

## 8. Performance Guidelines

### 8.1 Database Operations

**GORM best practices:**
- Use connection pooling
- Implement proper preloading for associations
- Use transactions for multiple operations
- Add appropriate database indexes

### 8.2 Caching Strategies

**Redis integration:**
- Use connection pooling
- Implement proper key naming conventions
- Set appropriate TTL values
- Handle cache misses gracefully

### 8.3 HTTP Client Configuration

**SDK timeout settings:**
```go
// Configure appropriate timeouts
ConnTimeOut = 5000    // 5 seconds
WriteTimeOut = 30000  // 30 seconds
ReadTimeOut = 30000   // 30 seconds
Retry = 2             // Retry failed requests
```

## 9. Monitoring and Observability

### 9.1 Logging Levels

**Use appropriate log levels:**
- `Debug`: Detailed debugging information
- `Trace`: API calls and detailed flow tracking
- `Notice`: Normal operation milestones
- `Warning`: Recoverable errors and issues
- `Error`: Serious errors requiring attention
- `Fatal`: Critical errors causing service failure

### 9.2 Metrics and Monitoring

**Component monitoring:**
- Log operation start/completion times
- Track error rates and types
- Monitor resource usage
- Implement health checks

### 9.3 Request Tracing

**Context propagation:**
```go
// Pass context through all function calls
func ProcessRequest(ctx context.Context, req *Request) error {
    logger.ComponentLogger.Trace(ctx, "processing request: %s", req.ID)

    result, err := externalService.Call(ctx, req)
    if err != nil {
        logger.ComponentLogger.Warning(ctx, "external call failed: %s", err)
        return err
    }

    return nil
}
```

## 10. Code Review Guidelines

### 10.1 Review Checklist

**Before submitting code:**
- [ ] All tests pass locally
- [ ] Code follows naming conventions
- [ ] Error handling is implemented
- [ ] Logging is appropriate
- [ ] Documentation is updated
- [ ] No hardcoded values
- [ ] Resource cleanup is handled

### 10.2 Review Criteria

**Code quality standards:**
- Functions should be focused and single-purpose
- Avoid deep nesting (max 3-4 levels)
- Use meaningful variable and function names
- Handle all error cases appropriately
- Include appropriate comments for complex logic

### 10.3 Performance Considerations

**Review for performance:**
- Avoid unnecessary allocations in hot paths
- Use appropriate data structures
- Consider memory usage patterns
- Optimize database queries
- Implement proper caching where beneficial

---

**Note:** This document should be updated as the codebase evolves. All developers must follow these standards to ensure consistency and maintainability.

**Last Updated:** 2025-07-23
**Version:** 1.0
